# 关键问题清单

## 一、技术能力核心问题

### 1. 中文和香港本地化能力 ⭐⭐⭐⭐⭐

**关键问题**:

- Amazon Bedrock对**繁体中文保险专业术语**的处理准确率？（需要具体数据）
- **中英文混合文档**的语义理解能力？（香港理赔文档常见格式）
- 对香港**法律条文和保险条款**的语义解析深度？
- **跨境理赔场景**（深圳湾、港珠澳大桥）的专业术语处理？

### 2. 多模态处理能力 ⭐⭐⭐⭐⭐

**关键问题**:

- Claude 3 Sonnet对**车辆损害评估的准确率**？（需要基准数据和误差范围）
- **伪造文档识别**的技术原理和准确率？（特别是香港常见的假发票）
- **多角度、不同光线条件**下的图像识别鲁棒性？
- **视频损害评估**的技术成熟度和实际应用案例？

### 3. 欺诈检测能力 ⭐⭐⭐⭐⭐

#### A. 事前预防：模型训练与部署 ⭐⭐⭐⭐⭐

**模型训练关键问题**:

- 基于**1200+历史案例**的模型训练方案和预期精度？
- **样本不平衡**的具体解决方案和效果验证？
- **自动特征工程**能力：能提取哪些关键欺诈特征？
- **最小有效样本量**要求和冷启动策略？
- **联邦学习**的技术实现和数据隐私保护机制？

**模型部署和更新**:

- **实时推理延迟**：具体响应时间SLA和性能基准？
- **模型漂移检测**：触发机制和自动重训练策略？
- **增量学习vs全量重训练**：技术选择和成本对比？
- **模型可解释性**：决策路径的透明度和监管合规性？

**预防性部署策略**:

- **关键检测节点**的选择依据和部署架构？
- **动态风险阈值**设置和业务影响评估？
- **误报/漏报平衡**：具体指标和优化策略？

#### B. 事中抑制+事后反哺：知识图谱 ⭐⭐⭐⭐⭐

**知识图谱构建**:

- **欺诈知识图谱**的技术架构和数据模型设计？
- **实体关系建模**：支持的复杂度和动态更新能力？
- **图数据库选型**：性能对比和扩展性分析？
- **规模限制**：百万级节点的查询性能和成本？

**事中实时抑制**:

- **实时图查询**：毫秒级响应的技术实现和性能保证？
- **复杂关联分析**：多跳路径发现的算法和效率？
- **异常模式检测**：图神经网络的准确率和计算成本？
- **隐蔽欺诈网络**：深层关联挖掘的技术深度？

**事后反哺机制**:

- **自动知识更新**：新案例的图谱融入机制和质量控制？
- **专家知识融合**：人工经验与AI学习的协同机制？
- **模式挖掘**：自动发现新欺诈模式的算法和验证？
- **反馈闭环**：图谱更新对模型性能提升的量化效果？

#### C. 两维度协同工作机制 ⭐⭐⭐⭐⭐

**协同检测策略**:

- **模型预测**和**图谱分析**结果如何融合？
- **多维度风险评分**的权重分配策略？
- 如何处理两个维度结果**冲突**的情况？
- **置信度**和**可解释性**的综合评估？

**香港特色欺诈场景应用**:

- **跨境欺诈网络**的图谱建模和检测？
- **修理厂串通网络**的关联分析？
- **重复理赔**的时空关联检测？
- **身份伪造**的多维度验证？

**演示要求**:

**事前预防演示**:

- 展示基于历史数据的模型训练过程
- 演示实时欺诈风险评分
- 展示模型解释性和决策依据
- 演示模型更新和版本管理

**事中抑制+事后反哺演示**:

- 展示知识图谱的构建和可视化
- 演示实时关联分析和异常检测
- 展示新案例的自动学习和图谱更新
- 演示复杂欺诈网络的发现过程

**协同工作演示**:

- 展示两个维度的结果融合
- 演示端到端的欺诈检测流程
- 展示人机协作的决策支持界面

## 二、系统集成和架构问题

### 4. 现有系统集成 ⭐⭐⭐⭐

**关键问题**:

- **API标准化**：支持哪些行业标准协议（REST、GraphQL、gRPC）？
- **数据迁移策略**：1200+历史案例的迁移方案和数据完整性保证？
- **渐进式迁移**：分阶段切换的技术方案和回滚机制？
- **集成测试**：自动化测试覆盖率和性能验证方法？

### 5. 数据安全和合规 ⭐⭐⭐⭐⭐

**关键问题**:

- 客户敏感数据在AWS云端的**加密存储**方案？
- 是否符合**香港个人资料私隐条例**？
- 数据是否可以存储在**香港本地**或指定区域？
- 如何确保数据不被用于AI模型训练？
- 是否符合**香港保险监管局(IA)**的数据保护要求？
- 对于**跨境数据传输**（香港-内地-国际）的合规处理？
- 是否支持**数据主权**要求（数据不出境）？
- 如何应对**国安法**对数据安全的要求？

**香港特殊合规要求**:

- 香港《个人资料（私隐）条例》第33条跨境数据传输
- 金融管理局对保险公司IT系统的监管要求
- 《网络安全法》在香港的适用性
- 一国两制下的数据治理框架

**合规要求**:

- 提供详细的数据安全认证文档
- 说明GDPR、HIPAA等合规措施
- 展示数据访问控制和审计功能
- 提供香港法律意见书（如需要）

### 6. 性能和可扩展性 ⭐⭐⭐⭐

**关键问题**:

- **并发处理能力**：峰值TPS和平均响应时间的具体指标？
- **自动扩展**：负载增长时的扩展策略和成本影响？
- **SLA保证**：可用性、响应时间的具体承诺和赔偿机制？
- **性能基准**：与同类产品的对比数据和测试报告？

## 三、成本和商业模式问题

### 7. 详细成本结构 ⭐⭐⭐⭐⭐

**关键问题**:

- **AI模型调用费用**的具体计算方式？
- **存储费用**如何计算？（按数据量还是按时间）
- 是否有**最低消费承诺**？
- **超量使用**的费用如何计算？

**成本细节**:

- 请提供基于我们业务量的详细报价
- 说明不同使用场景的成本差异
- 提供成本优化建议和控制机制

### 8. 服务和支持模式 ⭐⭐⭐⭐

**关键问题**:

- **香港本地支持团队**：技术人员数量、专业背景和响应能力？
- **分级响应机制**：P0/P1/P2问题的具体响应时间SLA？
- **中文技术支持**：能否处理复杂的保险业务和技术问题？
- **应急响应**：系统故障时的escalation流程和恢复时间目标？

## 四、实施和部署问题

### 9. 实施时间表和里程碑 ⭐⭐⭐⭐

**关键问题**:

- **完整系统部署**需要多长时间？
- **分阶段实施**的具体计划？
- 关键里程碑和**验收标准**？
- **用户培训**的时间和内容安排？

**项目管理**:

- 请提供详细的项目实施甘特图
- 说明各阶段的交付物和验收标准
- 展示项目风险管理机制

### 10. 定制化开发能力 ⭐⭐⭐⭐

**关键问题**:

- 针对香港保险法规的**定制化程度**？
- **玻璃保险**等特殊险种的适配能力？
- 与**外部合作伙伴**（修理厂等）的集成？
- **移动端应用**的开发和部署？
- 是否支持**香港特色业务流程**（如验车中心集成）？
- 能否适配**香港保险中介**的业务模式？
- 对**跨境车险**（粤港澳大湾区）的支持能力？

**香港特色定制需求**:

- 香港运输署车辆登记系统集成
- 香港汽车会(AA)验车报告处理
- 与香港主要修理厂网络的API对接
- 支持港币、人民币、美元多币种处理
- 适配香港保险代理人佣金结算系统

**定制化范围**:

- 说明标准功能和定制功能的边界
- 提供定制化开发的时间和成本估算
- 展示类似定制化项目的案例
- 提供香港本地化开发团队信息

## 五、风险管理和应急预案

### 11. 供应商锁定风险 ⭐⭐⭐⭐

**关键问题**:

- 如何避免过度依赖AWS生态系统？
- **数据导出**和**系统迁移**的可行性？
- 是否支持**混合云**或**多云**部署？
- 合同终止时的**数据交接**流程？

**风险缓解**:

- 提供数据可移植性的技术方案
- 说明标准化接口和开放性设计
- 展示数据备份和恢复机制

### 12. 技术更新和维护 ⭐⭐⭐

**关键问题**:

- **AI模型更新**：版本升级对现有业务的影响和兼容性保证？
- **维护窗口**：计划内维护的频率、时长和业务连续性保障？
- **技术债务管理**：长期维护成本和系统演进策略？

## 六、香港监管和政策适应性

### 13. 香港保险监管合规 ⭐⭐⭐⭐⭐

**关键问题**:

- 系统如何支持**香港保险监管局(IA)**的监管报告要求？
- 是否符合**《保险业条例》**的技术和运营要求？
- 如何处理**理赔投诉**和**争议解决**的监管要求？
- 对**偿付能力**计算和报告的支持能力？
- 是否支持**ORSA**（Own Risk and Solvency Assessment）报告？

**监管适应性**:

- 支持IA要求的理赔数据统计和报告
- 符合《保险业（一般业务）规则》的系统要求
- 适配香港保险投诉局(ICB)的投诉处理流程
- 支持强制性第三者责任保险的特殊要求

### 14. 粤港澳大湾区业务支持 ⭐⭐⭐⭐

**关键问题**:

- 是否支持**跨境车险**业务处理？
- 能否处理**港珠澳大桥**等跨境基建的理赔？
- 对**两地车牌**车辆的理赔支持？
- 如何处理**汇率波动**对理赔金额的影响？
- 是否支持与**内地保险公司**的数据共享？

**大湾区特色**:

- 深港通关口岸事故处理
- 跨境物流车辆保险理赔
- 大湾区居民跨境工作的保险需求
- 与内地监管机构的协调机制

## 七、竞争对比和差异化

### 15. 竞争优势分析 ⭐⭐⭐

**关键问题**:

- **技术差异化**：相比Google Cloud、Microsoft Azure的独特优势？
- **保险行业案例**：类似规模客户的实施效果和ROI数据？
- **大中华区优势**：本地化服务和合规支持的具体体现？

## 演示准备建议

### 现场演示环境要求

1. **演示环境**: 提供与生产环境一致的完整功能演示系统
2. **测试数据**: 准备香港保险业务场景的真实案例数据（已脱敏）
3. **性能测试**: 支持现场压力测试和并发处理验证

### 技术团队配置建议

1. **解决方案架构师**: 负责整体技术架构讲解
2. **AI/ML专家**: 负责AI模型和算法演示
3. **保险行业专家**: 负责业务场景和案例分析
4. **实施顾问**: 负责项目实施和部署计划
5. **商务代表**: 负责商业条款和合同讨论
6. **合规专家**: 负责香港法规和数据保护合规问题
7. **本地化专家**: 负责中文处理和香港本地化功能
8. **大中华区客户成功经理**: 提供区域化服务保障

### 会议资料准备清单

- [ ] **技术架构**：详细的系统架构图和数据流程图
- [ ] **成本分析**：基于业务量的详细报价和TCO模型
- [ ] **成功案例**：类似规模保险公司的实施案例和ROI数据
- [ ] **合规文档**：香港法律合规意见书和IA监管对应方案
- [ ] **API文档**：完整的接口规范和集成指南
- [ ] **性能报告**：基准测试数据和性能保证SLA

## 会议成功标准

### 技术评估标准

- [ ] 中文处理能力满足业务需求
- [ ] 多模态处理准确率达到预期
- [ ] 系统集成方案可行且风险可控
- [ ] 性能指标满足业务量要求
- [ ] **香港本地化**功能完善度评估
- [ ] **跨境业务**支持能力验证

### 商业评估标准

- [ ] 总体成本在预算范围内
- [ ] ROI预期符合投资要求
- [ ] 实施时间表可接受
- [ ] 风险管理方案完善
- [ ] **香港本地支持**服务质量评估

### 合规评估标准

- [ ] **香港个人资料私隐条例**合规性确认
- [ ] **IA监管要求**满足程度评估
- [ ] **数据主权**和跨境传输合规性
- [ ] **国安法**相关数据安全要求符合性

### 决策支持标准

- [ ] 获得足够的技术细节进行内部评估
- [ ] 明确下一步合作的具体条件
- [ ] 确定POC验证的范围和计划
- [ ] 建立后续沟通和评估机制
- [ ] **法律合规**风险评估完成
- [ ] **监管报告**要求明确化

---
