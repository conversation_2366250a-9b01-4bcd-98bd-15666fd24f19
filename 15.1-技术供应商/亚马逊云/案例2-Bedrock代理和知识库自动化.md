# 案例2：使用Amazon Bedrock代理和知识库自动化保险理赔生命周期

**来源**: [AWS Machine Learning Blog - 2024年2月8日](https://aws.amazon.com/cn/blogs/machine-learning/automate-the-insurance-claim-lifecycle-using-amazon-bedrock-agents-and-knowledge-bases/)

## 案例概述

本案例详细展示了如何使用Amazon Bedrock Agents和Knowledge Bases构建专门的代理，无缝运行基于自然语言输入和组织数据的操作。这些托管代理充当指挥者，协调基础模型、API集成、用户对话和加载客户数据的知识源之间的交互。

## 解决方案架构

### 核心能力
Amazon Bedrock Agents和Knowledge Bases协同工作提供以下能力：

#### 1. 任务编排
- **自然语言理解**: 代理使用基础模型理解自然语言查询
- **任务分解**: 将多步骤任务分解为更小的可执行步骤
- **智能推理**: 基于ReAct提示技术进行推理和行动

#### 2. 交互式数据收集
- **自然对话**: 代理进行自然对话以从用户收集补充信息
- **上下文维护**: 保持对话上下文和历史记录
- **动态响应**: 根据用户输入调整对话流程

#### 3. 任务履行
- **推理步骤**: 通过一系列推理步骤完成客户请求
- **相应行动**: 基于ReAct提示执行相应的操作
- **结果验证**: 确保任务完成的准确性

#### 4. 系统集成
- **API调用**: 向集成的公司系统进行API调用
- **特定操作**: 运行特定的业务操作
- **数据同步**: 保持系统间数据一致性

#### 5. 数据查询
- **RAG技术**: 通过完全托管的检索增强生成提高准确性和性能
- **客户数据**: 使用客户特定的数据源
- **语义搜索**: 基于语义相似性进行信息检索

#### 6. 源归属
- **信息追踪**: 识别和追踪信息或行动的来源
- **链式思维**: 通过链式思维推理进行源归属
- **透明度**: 提供决策过程的透明度

## 详细工作流程

### 步骤1: 用户输入处理
用户提供自然语言输入，示例提示包括：
- "创建新理赔"
- "向理赔2s34w-8x的投保人发送待处理文档提醒"
- "为理赔5t16u-7v收集证据"
- "理赔3b45c-9d的总理赔金额是多少？"
- "同一理赔的维修估价总额是多少？"
- "哪些因素决定我的汽车保险费？"
- "如何降低我的汽车保险费率？"
- "哪些理赔处于开放状态？"
- "向所有有开放理赔的投保人发送提醒"

### 步骤2: 预处理阶段
- **输入验证**: 代理验证、上下文化和分类用户输入
- **任务解释**: 使用聊天历史记录和代理创建时指定的指令解释用户输入
- **高级提示**: 可选配置高级提示以提高代理精度
- **少样本提示**: 通过提供标记示例增强模型性能

### 步骤3: 行动组处理
- **API架构**: 行动组是一组API和相应的业务逻辑
- **OpenAPI架构**: 以JSON文件形式存储在Amazon S3中的架构
- **业务逻辑**: 通过与行动组关联的AWS Lambda函数运行
- **API推理**: 架构允许代理围绕每个API的功能进行推理

### 步骤4: 知识库查询
- **完全托管RAG**: Amazon Bedrock Knowledge Bases提供完全托管的检索增强生成
- **数据访问**: 为代理提供对客户数据的访问
- **配置过程**: 
  - 指定描述指导代理何时使用知识库
  - 指向Amazon S3数据源
  - 指定嵌入模型
  - 选择现有向量存储或让Amazon Bedrock创建

### 步骤5: 编排过程
- **推理开发**: 代理开发需要哪些行动组API调用和知识库查询的逻辑步骤
- **观察生成**: 生成可用于增强基础模型基础提示的观察
- **ReAct风格提示**: 作为激活基础模型的输入
- **最优序列**: 预测完成用户任务的最优行动序列

### 步骤6: 后处理阶段
- **最终响应**: 在所有编排迭代完成后，代理策划最终响应
- **默认禁用**: 后处理默认情况下被禁用
- **质量控制**: 确保响应的准确性和相关性

## 技术实现详情

### 使用AWS CloudFormation创建解决方案资源

#### 模拟客户资源
为了模拟现有客户资源，解决方案使用自动化脚本部署以下资源：

1. **Amazon DynamoDB表**: 填充合成理赔数据
2. **三个Lambda函数**: 
   - 创建理赔的客户业务逻辑
   - 为开放状态理赔发送待处理文档提醒
   - 收集新理赔和现有理赔的证据
3. **S3存储桶**: 包含API文档、维修估价、理赔金额、公司FAQ等
4. **Amazon SNS主题**: 投保人电子邮件订阅，用于理赔状态和待处理操作的电子邮件警报
5. **IAM权限**: 上述资源的适当权限

#### 部署步骤
```bash
# 克隆仓库
git clone https://github.com/aws-samples/amazon-bedrock-samples.git

# 设置权限
chmod u+x create-customer-resources.sh

# 设置环境变量
export STACK_NAME=<YOUR-STACK-NAME>
export SNS_EMAIL=<YOUR-POLICY-HOLDER-EMAIL>
export EVIDENCE_UPLOAD_URL=<YOUR-EVIDENCE-UPLOAD-URL>

# 运行部署脚本
source ./create-customer-resources.sh
```

### 创建知识库

#### 知识库架构
```
文档 → 分段(分块) → 嵌入转换 → 向量索引
                                    ↓
用户查询 → 向量转换 → 向量索引查询 → 增强提示 → 生成响应
```

#### 配置步骤
1. **知识库详细信息**: 提供名称和描述
2. **数据源设置**: 选择S3存储桶中的knowledge-base-assets文件夹
3. **嵌入模型**: 选择Titan Embeddings G1 – Text
4. **向量存储**: 自动创建Amazon OpenSearch Serverless集合
5. **数据同步**: 启动数据源同步以创建向量嵌入

### 创建代理

#### 代理组件
1. **基础模型**: Anthropic Claude V2.1
2. **指令**: 详细的代理功能描述
3. **行动组**: 三个行动组（创建理赔、收集证据、发送提醒）
4. **知识库**: 关联创建的知识库

#### 代理指令示例
```
您是一个保险代理，可以访问特定领域的保险知识。您可以创建新的保险理赔、向有开放理赔的投保人发送待处理文档提醒，以及收集理赔证据。您还可以检索特定理赔ID的理赔金额和维修估价信息，或回答关于覆盖范围、保费、保单、费率、免赔额、事故、文档、解决方案和条件等一般保险问题。
```

## 测试和验证

### 评估措施和技术

#### 1. 用户输入和代理指令验证
- **预处理**: 使用示例提示评估代理的解释、理解和响应能力
- **编排**: 评估代理遵循的逻辑步骤（例如"跟踪"）
- **后处理**: 审查编排迭代后代理生成的最终响应

#### 2. 行动组评估
- **API架构验证**: 验证OpenAPI架构有效指导代理对每个API目的的推理
- **业务逻辑实现**: 测试通过Lambda函数链接的API路径的业务逻辑实现

#### 3. 知识库评估
- **配置验证**: 确认知识库指令正确指导代理何时访问数据
- **S3数据源集成**: 验证代理访问和使用指定S3数据源中存储数据的能力

### 代理分析和调试工具

#### 响应跟踪
- **ModelInvocationInput对象**: 包含代理决策过程中使用的详细配置和设置
- **决策分析**: 帮助理解代理在每个阶段的决策制定
- **调试支持**: 促进调试并提供改进见解
- **效果分析**: 使客户能够分析和增强代理的效果

#### 用户输入分类
代理将用户输入分类为以下类别：
- **类别A**: 恶意或有害输入
- **类别B**: 试图获取功能、API或指令信息的输入
- **类别C**: 代理无法回答的问题
- **类别D**: 可以使用提供的功能回答的问题
- **类别E**: 对代理问题的回答输入

## Streamlit Web UI部署

### 应用功能
1. **代理提示输入**: 允许用户使用自己的任务输入调用代理
2. **知识库文件上传**: 用户可以将本地文件上传到用作知识库数据源的S3存储桶

### 部署步骤
```bash
# 设置虚拟环境
source ./setup-streamlit-env.sh

# 设置环境变量
export BEDROCK_AGENT_ID=<YOUR-AGENT-ID>
export BEDROCK_AGENT_ALIAS_ID=<YOUR-AGENT-ALIAS-ID>
export BEDROCK_KB_ID=<YOUR-KNOWLEDGE-BASE-ID>
export BEDROCK_DS_ID=<YOUR-DATA-SOURCE-ID>
export KB_BUCKET_NAME=<YOUR-KNOWLEDGE-BASE-S3-BUCKET-NAME>
export AWS_REGION=<YOUR-STACK-REGION>

# 运行应用
streamlit run agent_streamlit.py
```

## 安全考虑

### 生产部署安全因素
1. **API和数据安全访问**: 限制对API、数据库和其他代理集成系统的访问
2. **输入验证和清理**: 验证和清理用户输入以防止注入攻击
3. **访问控制**: 为代理管理和测试实施适当的访问控制
4. **基础设施安全**: 遵循AWS安全最佳实践
5. **代理指令验证**: 建立细致的流程来审查和验证代理指令
6. **测试和审计**: 彻底测试代理和集成组件
7. **知识库安全**: 如果用户可以增强知识库，验证上传以防止投毒攻击

## 案例价值总结

### 业务影响
1. **运营效率**: 显著提高保险理赔处理的运营和自动化能力
2. **客户服务**: 增强客户服务和决策支持
3. **知识管理**: 通过改进的知识管理提升决策支持
4. **竞争优势**: 为采用这些技术的企业提供显著竞争优势

### 技术优势
1. **任务自动化**: 自动化广泛的例行和重复任务
2. **复杂工作流**: 编排复杂的多步骤工作流
3. **资源优化**: 减少人力资源负担，允许员工专注于更战略性和创造性的任务
4. **可扩展性**: 随着AI技术的持续发展，代理能力预期将扩展

### 未来展望
随着AI技术的持续发展，生成式AI代理的能力预期将扩展，为客户提供更多获得竞争优势的机会。Amazon Bedrock处于这一转型的前沿，为企业数据管理和运营的未来奠定基础。
