# 电子报案系统API设计规范
## 香港立桥保险财险理赔管理部

### 文档说明
本文档定义了电子报案系统的API设计规范，包括接口定义、数据格式、错误处理、安全认证等技术细节，为系统开发和集成提供标准化指导。

---

## 一、API设计原则

### 1.1 RESTful设计原则
- [ ] **资源导向**：URL表示资源，HTTP方法表示操作
- [ ] **无状态**：每个请求包含完整的处理信息
- [ ] **统一接口**：标准化的HTTP方法和状态码
- [ ] **分层系统**：支持缓存、负载均衡等中间层
- [ ] **可缓存**：合理使用HTTP缓存机制

### 1.2 API版本管理
```
URL版本控制：
https://api.wli.com.hk/v1/claims/reports
https://api.wli.com.hk/v2/claims/reports

Header版本控制：
Accept: application/vnd.wli.v1+json
Accept: application/vnd.wli.v2+json
```

### 1.3 命名规范
- [ ] **URL路径**：使用小写字母，单词间用连字符分隔
- [ ] **资源名称**：使用复数形式表示集合
- [ ] **查询参数**：使用下划线分隔单词
- [ ] **JSON字段**：使用驼峰命名法

---

## 二、核心API接口定义

### 2.1 报案管理API

#### 2.1.1 提交报案
```http
POST /api/v1/claims/reports
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "policyNumber": "POL2024001234",
  "incidentType": "vehicle",
  "incidentDate": "2024-01-15T10:30:00Z",
  "location": {
    "latitude": 22.3193,
    "longitude": 114.1694,
    "address": "香港中环皇后大道中1号"
  },
  "description": "车辆在中环发生碰撞事故",
  "estimatedAmount": 50000.00,
  "reporterInfo": {
    "name": "张三",
    "phone": "+852-12345678",
    "email": "<EMAIL>",
    "relationship": "policyholder"
  },
  "attachments": [
    {
      "fileName": "accident_photo_1.jpg",
      "fileType": "image/jpeg",
      "fileSize": 2048576,
      "base64Data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
    }
  ]
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "reportId": "CR202401150001",
    "status": "submitted",
    "submissionTime": "2024-01-15T10:35:00Z",
    "estimatedProcessingTime": "2024-01-17T10:35:00Z",
    "riskScore": 25,
    "riskLevel": "low",
    "nextSteps": [
      "等待初步审核",
      "可能需要补充材料",
      "预计2个工作日内联系"
    ]
  },
  "message": "报案提交成功",
  "timestamp": "2024-01-15T10:35:00Z",
  "requestId": "req_123456789"
}
```

#### 2.1.2 查询报案状态
```http
GET /api/v1/claims/reports/{reportId}
Authorization: Bearer {access_token}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "reportId": "CR202401150001",
    "policyNumber": "POL2024001234",
    "status": "under_review",
    "currentStep": "risk_assessment",
    "progress": 30,
    "timeline": [
      {
        "step": "submitted",
        "timestamp": "2024-01-15T10:35:00Z",
        "description": "报案已提交"
      },
      {
        "step": "initial_review",
        "timestamp": "2024-01-15T14:20:00Z",
        "description": "初步审核完成"
      },
      {
        "step": "risk_assessment",
        "timestamp": "2024-01-16T09:15:00Z",
        "description": "风险评估中",
        "current": true
      }
    ],
    "assignedTo": {
      "name": "李理赔员",
      "phone": "+852-87654321",
      "email": "<EMAIL>"
    },
    "estimatedCompletionTime": "2024-01-18T17:00:00Z"
  }
}
```

#### 2.1.3 上传补充材料
```http
POST /api/v1/claims/reports/{reportId}/attachments
Content-Type: multipart/form-data
Authorization: Bearer {access_token}

files: [File1, File2, ...]
description: "补充的维修发票和照片"
attachmentType: "supplementary"
```

#### 2.1.4 获取报案列表
```http
GET /api/v1/claims/reports?page=1&size=20&status=submitted&sort=created_at:desc
Authorization: Bearer {access_token}
```

### 2.2 文件管理API

#### 2.2.1 文件上传
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {access_token}

file: [File]
category: "claim_document"
description: "事故现场照片"
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "fileId": "FILE_20240115_001",
    "fileName": "accident_scene.jpg",
    "fileUrl": "https://files.wli.com.hk/claims/2024/01/15/accident_scene.jpg",
    "fileSize": 2048576,
    "mimeType": "image/jpeg",
    "uploadTime": "2024-01-15T10:40:00Z",
    "ocrResult": {
      "detected": true,
      "text": "车牌号：HK1234",
      "confidence": 0.95
    },
    "aiAnalysis": {
      "damageDetected": true,
      "damageLevel": "moderate",
      "estimatedCost": 15000.00
    }
  }
}
```

#### 2.2.2 文件下载
```http
GET /api/v1/files/{fileId}/download
Authorization: Bearer {access_token}
```

### 2.3 AI服务API

#### 2.3.1 OCR文字识别
```http
POST /api/v1/ai/ocr
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "imageUrl": "https://files.wli.com.hk/temp/document.jpg",
  "documentType": "id_card",
  "language": "zh-cn"
}
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "recognitionId": "OCR_20240115_001",
    "documentType": "hk_id_card",
    "extractedInfo": {
      "name": "張三",
      "idNumber": "A123456(7)",
      "dateOfBirth": "1990-01-01",
      "issueDate": "2020-01-01"
    },
    "confidence": 0.98,
    "processingTime": 1.2
  }
}
```

#### 2.3.2 图像损伤分析
```http
POST /api/v1/ai/damage-analysis
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "imageUrl": "https://files.wli.com.hk/claims/vehicle_damage.jpg",
  "vehicleType": "sedan",
  "analysisType": "vehicle_damage"
}
```

#### 2.3.3 风险评估
```http
POST /api/v1/ai/risk-assessment
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "reportId": "CR202401150001",
  "claimData": {
    "policyNumber": "POL2024001234",
    "incidentType": "vehicle",
    "estimatedAmount": 50000.00,
    "customerHistory": {
      "previousClaims": 2,
      "customerSince": "2020-01-01"
    }
  }
}
```

### 2.4 通知服务API

#### 2.4.1 发送通知
```http
POST /api/v1/notifications/send
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "recipients": [
    {
      "type": "customer",
      "customerId": "CUST_001",
      "channels": ["sms", "email", "push"]
    }
  ],
  "template": "claim_status_update",
  "data": {
    "reportId": "CR202401150001",
    "status": "approved",
    "amount": 45000.00
  },
  "priority": "high",
  "scheduledTime": "2024-01-15T15:00:00Z"
}
```

---

## 三、数据格式规范

### 3.1 通用响应格式
```json
{
  "success": boolean,
  "data": object | array | null,
  "message": string,
  "error": {
    "code": string,
    "message": string,
    "details": object
  },
  "pagination": {
    "page": number,
    "size": number,
    "total": number,
    "totalPages": number
  },
  "timestamp": string,
  "requestId": string
}
```

### 3.2 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "policyNumber",
      "message": "保单号格式不正确",
      "value": "INVALID_POLICY"
    }
  },
  "timestamp": "2024-01-15T10:35:00Z",
  "requestId": "req_123456789"
}
```

### 3.3 分页查询格式
```http
GET /api/v1/claims/reports?page=1&size=20&sort=created_at:desc&filter=status:submitted
```

**查询参数说明**：
- `page`: 页码，从1开始
- `size`: 每页记录数，默认20，最大100
- `sort`: 排序字段和方向，格式：field:asc|desc
- `filter`: 过滤条件，格式：field:value

---

## 四、安全认证规范

### 4.1 OAuth 2.0认证流程
```
1. 客户端向认证服务器请求授权码
   GET /oauth/authorize?client_id=xxx&response_type=code&redirect_uri=xxx

2. 用户授权后获取授权码
   Redirect: https://client.com/callback?code=AUTH_CODE

3. 使用授权码换取访问令牌
   POST /oauth/token
   {
     "grant_type": "authorization_code",
     "code": "AUTH_CODE",
     "client_id": "CLIENT_ID",
     "client_secret": "CLIENT_SECRET"
   }

4. 获取访问令牌
   {
     "access_token": "ACCESS_TOKEN",
     "token_type": "Bearer",
     "expires_in": 3600,
     "refresh_token": "REFRESH_TOKEN"
   }
```

### 4.2 JWT令牌格式
```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user_123",
    "iss": "liqiao-auth-server",
    "aud": "liqiao-api",
    "exp": 1642248000,
    "iat": 1642244400,
    "roles": ["customer", "claim_reporter"],
    "permissions": ["claim:create", "claim:read"]
  }
}
```

### 4.3 API密钥认证
```http
GET /api/v1/claims/reports
X-API-Key: your_api_key_here
X-API-Secret: your_api_secret_here
X-Timestamp: 1642244400
X-Signature: calculated_signature
```

---

## 五、错误处理规范

### 5.1 HTTP状态码使用
- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未授权访问
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突
- `422 Unprocessable Entity`: 业务逻辑错误
- `429 Too Many Requests`: 请求频率限制
- `500 Internal Server Error`: 服务器内部错误

### 5.2 业务错误码定义
```json
{
  "VALIDATION_ERROR": "参数验证错误",
  "POLICY_NOT_FOUND": "保单不存在",
  "POLICY_EXPIRED": "保单已过期",
  "DUPLICATE_REPORT": "重复报案",
  "INSUFFICIENT_PERMISSION": "权限不足",
  "FILE_TOO_LARGE": "文件过大",
  "UNSUPPORTED_FILE_TYPE": "不支持的文件类型",
  "OCR_PROCESSING_FAILED": "OCR识别失败",
  "RISK_ASSESSMENT_FAILED": "风险评估失败",
  "NOTIFICATION_SEND_FAILED": "通知发送失败"
}
```

### 5.3 错误处理最佳实践
```javascript
// 客户端错误处理示例
try {
  const response = await fetch('/api/v1/claims/reports', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(reportData)
  });

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(result.error.message);
  }
  
  return result.data;
} catch (error) {
  console.error('API调用失败:', error.message);
  // 错误处理逻辑
}
```

---

## 六、性能优化规范

### 6.1 缓存策略
```http
# 静态资源缓存
Cache-Control: public, max-age=31536000
ETag: "abc123"

# API响应缓存
Cache-Control: private, max-age=300
Vary: Authorization

# 条件请求
If-None-Match: "abc123"
If-Modified-Since: Wed, 21 Oct 2015 07:28:00 GMT
```

### 6.2 分页和限流
```http
# 分页响应头
X-Total-Count: 1000
X-Page-Count: 50
Link: <https://api.example.com/claims?page=2>; rel="next",
      <https://api.example.com/claims?page=50>; rel="last"

# 限流响应头
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

### 6.3 压缩和优化
```http
# 请求压缩
Accept-Encoding: gzip, deflate, br
Content-Encoding: gzip

# 响应优化
Content-Type: application/json; charset=utf-8
Transfer-Encoding: chunked
```

---

## 七、API文档和测试

### 7.1 OpenAPI规范
```yaml
openapi: 3.0.3
info:
  title: 电子报案系统API
  version: 1.0.0
  description: 香港立桥保险电子报案系统API文档
servers:
  - url: https://api.wli.com.hk/v1
    description: 生产环境
  - url: https://api-staging.wli.com.hk/v1
    description: 测试环境

paths:
  /claims/reports:
    post:
      summary: 提交报案
      tags:
        - 报案管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClaimReport'
      responses:
        '201':
          description: 报案提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
```

### 7.2 API测试用例
```javascript
// Jest测试示例
describe('报案API测试', () => {
  test('提交报案成功', async () => {
    const reportData = {
      policyNumber: 'POL2024001234',
      incidentType: 'vehicle',
      incidentDate: '2024-01-15T10:30:00Z',
      description: '测试报案'
    };

    const response = await request(app)
      .post('/api/v1/claims/reports')
      .set('Authorization', `Bearer ${testToken}`)
      .send(reportData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.reportId).toBeDefined();
  });

  test('无效保单号报案失败', async () => {
    const reportData = {
      policyNumber: 'INVALID_POLICY',
      incidentType: 'vehicle'
    };

    const response = await request(app)
      .post('/api/v1/claims/reports')
      .set('Authorization', `Bearer ${testToken}`)
      .send(reportData)
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});
```

---

## 八、API监控和日志

### 8.1 监控指标
- [ ] **响应时间**：P50, P95, P99响应时间
- [ ] **错误率**：4xx和5xx错误率
- [ ] **吞吐量**：每秒请求数(RPS)
- [ ] **可用性**：服务可用性百分比

### 8.2 日志格式
```json
{
  "timestamp": "2024-01-15T10:35:00Z",
  "level": "INFO",
  "service": "claim-report-api",
  "traceId": "trace_123456",
  "spanId": "span_789012",
  "method": "POST",
  "path": "/api/v1/claims/reports",
  "statusCode": 201,
  "responseTime": 245,
  "userId": "user_123",
  "requestId": "req_123456789",
  "userAgent": "Mozilla/5.0...",
  "clientIp": "*************"
}
```

---

**文档版本**：v1.0  
**编制日期**：2025年1月  
**编制部门**：财险理赔管理部  
**审核状态**：待审核
