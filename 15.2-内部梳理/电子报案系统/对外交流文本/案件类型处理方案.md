# 案件类型处理方案

## 电子报案系统差异化处理设计

### 方案概述

针对不同类型的保险案件（如玻璃险、交通意外、财产损失等），设计差异化的报案流程和信息收集方案，确保每种案件类型都能收集到必要的信息和资料。

---

## 一、案件类型分类体系

### 1.1 主要案件类型

```
车险类案件
├── 交通意外
├── 玻璃损坏（优先实施)
├── 车辆盗抢
├── 自然灾害损失
└── 第三者责任

财产险案件
├── 火灾损失
├── 水损事故
├── 盗抢损失
├── 自然灾害
└── 意外损坏

人身险案件
├── 意外伤害
├── 疾病医疗
├── 住院治疗
├── 身故理赔
└── 残疾理赔
```

### 1.2 案件识别机制

**自动识别**：

- 根据保单类型自动判断可能的案件类型
- 基于关键词识别事故描述中的案件类型
- 通过上传照片的AI分析识别损失类型

**用户选择**：

- 提供清晰的案件类型选择界面
- 每种类型配有图标和简要说明
- 支持"不确定"选项，由系统后续判断

---

## 二、差异化信息收集设计

### 2.1 车险 - 交通意外案件

#### 必填信息

```
基本信息：
├── 事故发生时间（精确到分钟）
├── 事故发生地点（详细地址+GPS）
├── 天气状况（晴天/雨天/雾天等）
├── 道路状况（干燥/湿滑/结冰等）
└── 事故经过详述

车辆信息：
├── 车牌号码
├── 驾驶员信息（姓名、驾照号）
├── 车辆损坏部位描述
├── 是否可以正常行驶
└── 车辆当前位置

第三方信息：
├── 对方车辆信息（如有）
├── 对方驾驶员信息
├── 对方保险公司信息
├── 是否有人员伤亡
└── 现场是否报警
```

#### 必需资料

```
现场照片：
├── 事故现场全景照片（至少2张）
├── 车辆损坏部位照片
├── 对方车辆照片（如有）
├── 道路标识照片
└── 车牌照片

证件资料：
├── 驾驶证照片
├── 行驶证照片
├── 身份证照片
├── 保险单照片
└── 交警事故认定书（如有）
```

### 2.2 车险 - 玻璃损坏案件

#### 必填信息

```
基本信息：
├── 损坏发现时间
├── 损坏发生地点
├── 损坏原因（石子击打/高温爆裂/人为损坏/不明原因）
├── 玻璃损坏程度（裂纹/破洞/完全破碎）
└── 是否影响驾驶安全

车辆信息：
├── 车牌号码
├── 损坏玻璃位置（前挡风/后挡风/侧窗等）
├── 玻璃品牌和型号（如知道）
├── 车辆年份和型号
└── 是否有贴膜
```

#### 必需资料

```
损坏照片：
├── 玻璃损坏全景照片
├── 损坏细节特写照片
├── 车辆外观照片
└── 车牌照片

证件资料：
├── 行驶证照片
├── 身份证照片
├── 保险单照片
└── 购车发票（如需要）
```

#### 特殊处理流程

```
玻璃险快速通道：
1. 损失金额评估 < 5000港币
2. 无争议的单纯玻璃损坏
3. 自动进入快速理赔流程
4. 24小时内完成审核
5. 推荐合作维修厂
```

### 2.3 财产险 - 火灾损失案件

#### 必填信息

```
基本信息：
├── 火灾发生时间
├── 火灾发生地点
├── 火灾原因（电器故障/燃气泄漏/外来火源/不明原因）
├── 火灾扑灭时间
├── 是否报警/消防部门是否到场
├── 是否有人员伤亡
└── 受损财产清单

损失评估：
├── 建筑结构损坏程度
├── 家具家电损失清单
├── 重要物品损失（珠宝/艺术品等）
├── 临时住宿需求
└── 预估总损失金额
```

#### 必需资料

```
现场照片：
├── 火灾现场全景照片
├── 受损区域详细照片
├── 重要物品损坏照片
├── 建筑结构损坏照片
└── 周边环境照片

官方文件：
├── 消防部门出具的火灾证明
├── 警方报案回执（如有）
├── 物业管理处证明
├── 财产清单和购买凭证
└── 临时住宿费用收据
```

### 2.4 人身险 - 意外伤害案件

#### 必填信息

```
基本信息：
├── 意外发生时间
├── 意外发生地点
├── 意外原因详述
├── 受伤部位和程度
├── 是否立即就医
├── 就医医院名称
└── 当前治疗状况

医疗信息：
├── 诊断结果
├── 治疗方案
├── 住院情况（如有）
├── 预计治疗费用
└── 是否影响工作
```

#### 必需资料

```
医疗资料：
├── 医院诊断证明
├── 医疗费用发票
├── 检查报告（X光/CT等）
├── 住院病历（如有）
└── 医生处方

身份证明：
├── 身份证照片
├── 保险单照片
├── 银行卡照片（理赔用）
└── 意外现场照片（如有）
```

---

## 三、动态表单设计

### 3.1 智能表单系统

```
表单生成逻辑：
保单类型识别 → 案件类型选择 → 动态生成对应表单
    ↓
根据用户输入动态调整后续字段
    ↓
智能提示必需资料和可选资料
    ↓
实时验证信息完整性
```

### 3.2 表单配置管理

**配置化设计**：

- 每种案件类型对应一套表单配置
- 支持字段的增加、删除、修改
- 支持必填/可选字段设置
- 支持字段间的逻辑关联

**版本管理**：

- 表单配置支持版本控制
- 新版本向下兼容
- 支持A/B测试不同表单设计
- 可根据数据分析优化表单结构

### 3.3 用户体验优化

**分步填写**：

```
第1步：基本信息（必填）
第2步：详细信息（根据案件类型动态生成）
第3步：资料上传（智能推荐需要的资料类型）
第4步：信息确认（最终检查和提交）
```

**智能提示**：

- 根据案件类型提示需要准备的资料
- 实时检查信息完整性
- 提供填写示例和帮助说明
- 支持保存草稿，随时继续填写

---

## 四、资料收集差异化

### 4.1 智能资料推荐

```
资料推荐逻辑：
案件类型 + 损失程度 → 推荐资料清单
    ↓
用户上传资料 → AI识别资料类型
    ↓
检查资料完整性 → 提示缺失资料
    ↓
质量检查 → 提示重新上传不清晰资料
```

### 4.2 资料分类管理

**自动分类**：

- 上传时自动识别资料类型
- 按类型自动归档整理
- 重复资料自动去重
- 不符合要求的资料自动标记

**质量控制**：

- 照片清晰度检查
- 文件完整性验证
- 必需信息是否包含
- 文件格式是否符合要求

### 4.3 特殊资料处理

**敏感资料**：

- 身份证等敏感信息加密存储
- 医疗资料隐私保护
- 财务信息安全处理
- 访问权限严格控制

**大文件处理**：

- 支持视频文件上传（监控录像等）
- 断点续传功能
- 文件压缩优化
- 云存储集成

---

## 五、业务规则引擎

### 5.1 规则配置系统

```
业务规则类型：
├── 字段显示规则（根据案件类型显示不同字段）
├── 必填字段规则（不同案件类型的必填要求）
├── 资料要求规则（每种案件需要的资料清单）
├── 审核流程规则（不同案件的审核路径）
└── 理赔限额规则（快速理赔的金额限制）
```

### 5.2 规则执行引擎

**实时执行**：

- 用户操作时实时应用规则
- 动态调整界面显示
- 即时验证输入内容
- 智能提示下一步操作

**规则优先级**：

- 法律法规要求（最高优先级）
- 公司政策要求
- 业务流程要求
- 用户体验优化

### 5.3 规则维护管理

**可视化配置**：

- 图形化规则配置界面
- 拖拽式流程设计
- 实时预览规则效果
- 批量规则导入导出

**测试验证**：

- 规则变更前测试验证
- 影响范围分析
- 回滚机制
- 变更日志记录

---

## 六、实施建议

### 6.1 分阶段实施

**第一阶段**：实现主要案件类型

- 交通意外
- 玻璃损坏
- 火灾损失
- 意外伤害

**第二阶段**：完善细分类型

- 增加更多细分案件类型
- 优化表单设计
- 完善业务规则

**第三阶段**：智能化升级

- AI自动识别案件类型
- 智能资料推荐
- 自动化审核流程

### 6.2 技术实现要点

**前端实现**：

- 动态表单组件
- 条件渲染逻辑
- 文件上传组件
- 进度提示组件

**后端实现**：

- 规则引擎系统
- 表单配置管理
- 文件处理服务
- 业务流程引擎

### 6.3 测试验证

**功能测试**：

- 各案件类型表单功能
- 规则引擎执行正确性
- 文件上传和处理
- 数据完整性验证

**用户测试**：

- 不同案件类型的用户体验
- 表单填写便利性
- 资料上传体验
- 整体流程顺畅性

---

**总结**：通过差异化的案件类型处理方案，确保每种保险案件都能收集到准确、完整的信息，提升理赔处理效率和客户体验。
