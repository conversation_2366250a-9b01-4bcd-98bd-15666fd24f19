# 电子报案系统统一需求方案
## 香港立桥保险财险理赔管理部

### 文档说明
本文档是电子报案系统的统一需求方案，整合了业务需求、技术架构、功能规格等所有核心内容，为系统建设提供完整的指导。

---

## 一、项目概述

### 1.1 项目背景
电子报案系统是理赔科技化转型的核心入口，旨在为客户提供便捷、高效的报案体验，同时为公司建立智能化的案件处理流程，提升理赔效率，降低运营成本。

### 1.2 核心设计理念
- **统一入口**：所有报案通过统一的网页链接进行
- **全渠道导流**：微信、WhatsApp、邮件、电话等渠道都引导客户访问统一报案网页
- **链接内认证**：在网页内完成身份验证和保单选择
- **证据链完整**：从报案到结案的完整操作记录

### 1.3 项目目标
- **提升客户体验**：提供7×24小时便捷报案服务
- **提高处理效率**：通过AI技术实现信息自动提取和初步审核
- **降低运营成本**：减少人工处理环节，提升自动化水平
- **加强风险控制**：建立智能风险评估机制，及早识别异常案件
- **完善数据管理**：建立完整的报案数据档案，支持后续分析

---

## 二、系统架构设计

### 2.1 统一报案入口设计
**核心架构**：
```
统一报案网页 (https://claims.wli.com.hk/report)
├── 身份认证模块
├── 保单选择模块
├── 报案信息填写模块
├── 资料上传模块
├── 进度跟踪模块
└── 客服支持模块
```

**多渠道导流架构**：
```
客户接触点                    统一报案网页
├── 微信小程序     ────────→  
├── 微信公众号     ────────→  
├── WhatsApp      ────────→   https://claims.wli.com.hk/report
├── 邮件系统      ────────→  
├── 电话客服      ────────→  
└── 官网入口      ────────→  
```

### 2.2 技术架构
**前端技术栈**：
- HTML5 + CSS3 + JavaScript
- 响应式设计框架
- 现代浏览器兼容
- 移动端优化

**后端技术栈**：
- RESTful API架构
- 微服务设计
- 数据库集群
- 缓存系统

**基础设施**：
- 云原生部署
- 负载均衡
- CDN加速
- 安全防护

---

## 三、功能需求规格

### 3.1 身份认证模块
**认证流程**：
```
第1步：输入手机号码
  ├── 格式验证：香港手机号格式
  ├── 存在验证：系统中是否存在该手机号
  └── 发送验证码

第2步：输入短信验证码
  ├── 6位数字验证码
  ├── 5分钟有效期
  └── 最多5次尝试

第3步：输入身份证后4位
  ├── 与手机号关联的身份证匹配
  ├── 最多3次尝试
  └── 验证通过进入保单选择
```

**安全特性**：
- 防暴力破解：多次失败自动锁定
- 会话管理：安全会话控制
- 设备记忆：可选记住设备
- 异常检测：异常访问自动告警

### 3.2 保单选择模块
**保单展示**：
```
保单列表显示：
├── 保单号码
├── 保险产品名称
├── 保险期间
├── 保单状态
├── 被保险人信息
└── 选择按钮
```

**功能特性**：
- 自动加载：认证通过后自动显示客户所有有效保单
- 搜索筛选：支持保单号、产品类型搜索
- 详情查看：点击查看保单详细信息
- 状态验证：只显示可报案的有效保单

### 3.3 报案信息填写模块

#### 3.3.1 案件类型识别
**自动识别机制**：
- 根据保单类型自动判断可能的案件类型
- 基于关键词识别事故描述中的案件类型
- 通过上传照片的AI分析识别损失类型

**用户选择界面**：
```
主要案件类型：
车险类案件
├── 交通意外
├── 玻璃损坏
├── 车辆盗抢
├── 自然灾害损失
└── 第三者责任

财产险案件
├── 火灾损失
├── 水损事故
├── 盗抢损失
├── 自然灾害
└── 意外损坏

人身险案件
├── 意外伤害
├── 疾病医疗
├── 住院治疗
└── 其他情况
```

#### 3.3.2 动态表单设计
**智能表单生成**：
```
表单生成逻辑：
保单类型识别 → 案件类型选择 → 动态生成对应表单
    ↓
根据用户输入动态调整后续字段
    ↓
智能提示必需资料和可选资料
    ↓
实时验证信息完整性
```

**分步填写流程**：
```
第1步：案件类型选择
第2步：基本信息填写（必填）
第3步：详细信息填写（根据案件类型动态生成）
第4步：资料上传（智能推荐需要的资料类型）
第5步：信息确认（最终检查和提交）
```

#### 3.3.3 差异化信息收集

**交通意外案件**：
- 事故发生时间（精确到分钟）
- 事故发生地点（详细地址+GPS）
- 天气状况、道路状况
- 车辆信息、驾驶员信息
- 第三方信息、人员伤亡情况
- 必需资料：现场照片、证件资料、交警认定书

**玻璃损坏案件**：
- 损坏发现时间、损坏原因
- 玻璃损坏程度、位置
- 车辆信息、玻璃品牌型号
- 必需资料：损坏照片、证件资料
- 特殊处理：< 5000港币自动快速通道

**火灾损失案件**：
- 火灾发生时间、原因
- 消防部门是否到场
- 受损财产清单、损失评估
- 必需资料：现场照片、消防证明、财产凭证

**意外伤害案件**：
- 意外发生时间、地点、原因
- 受伤部位、就医情况
- 诊断结果、治疗方案
- 必需资料：医疗证明、费用发票、检查报告

**智能辅助功能**：
- GPS定位：自动获取当前位置
- 时间预填：默认当前时间
- 模板选择：根据案件类型提供填写模板
- 实时保存：输入内容自动保存
- 智能提示：根据案件类型提示需要的信息和资料

### 3.4 资料上传模块

#### 3.4.1 智能资料推荐
**推荐逻辑**：
```
资料推荐系统：
案件类型 + 损失程度 → 推荐资料清单
    ↓
用户上传资料 → AI识别资料类型
    ↓
检查资料完整性 → 提示缺失资料
    ↓
质量检查 → 提示重新上传不清晰资料
```

**分类管理**：
- 自动分类：上传时自动识别资料类型
- 按类型归档：现场照片、证件资料、官方文件等
- 重复检测：自动去重重复上传的资料
- 质量控制：照片清晰度检查、文件完整性验证

#### 3.4.2 上传功能设计
**上传方式**：
```
文件上传支持：
├── 拖拽上传
├── 点击选择文件
├── 移动端拍照上传
├── 文件预览
├── 批量上传
└── 扫描上传（证件类）
```

**文件处理**：
- 格式支持：图片、PDF、Word、Excel、视频等
- 大小限制：单文件最大100MB，视频文件最大500MB
- 进度显示：实时显示上传进度和处理状态
- 断点续传：支持大文件断点续传
- 压缩优化：自动压缩大图片文件

#### 3.4.3 特殊资料处理
**敏感资料保护**：
- 身份证等敏感信息加密存储
- 医疗资料隐私保护
- 财务信息安全处理
- 访问权限严格控制

**资料验证**：
- 证件真伪初步验证
- OCR识别关键信息
- 与填写信息交叉验证
- 异常资料自动标记

### 3.5 智能处理与防诈骗功能

#### 3.5.1 OCR识别能力
**文档识别**：
- 身份证件识别：香港身份证、内地身份证、护照等
- 保险单据识别：保单、理赔申请书、发票、收据等
- 车辆证件识别：行驶证、驾驶证、车辆登记证等
- 识别准确率≥95%，处理速度≤5秒/页

**真实性验证**：
- 证件真伪检测：基于AI的证件防伪验证
- 信息交叉验证：OCR结果与填写信息对比
- 元数据分析：文档拍摄时间、设备信息验证

#### 3.5.2 图像智能分析与防伪
**损伤识别分析**：
- 车辆损伤分析：自动识别车辆外观损伤部位和程度
- 玻璃损坏检测：裂纹形状、损坏程度、位置合理性分析
- 损失评估：初步评估维修成本和工时
- 事故分析：分析事故类型和责任判定

**图像真实性检测**：
```
多层次验证机制：
├── 第一层：基础损坏识别
│   ├── 裂纹形状分析
│   ├── 损坏程度评估
│   └── 位置合理性判断
├── 第二层：真实性验证
│   ├── 物理特征一致性
│   ├── 光线阴影合理性
│   └── 材质反射特性
└── 第三层：篡改检测
    ├── 像素级异常分析
    ├── 压缩痕迹检测
    └── 编辑工具特征识别
```

**照片质量标准化**：
- 分辨率要求：最低1920x1080像素
- 拍摄距离：距离损坏部位30-50厘米
- 拍摄角度：垂直于表面±15度
- 光线条件：自然光或充足室内光
- 参照物要求：包含硬币或标尺作为尺寸参考

#### 3.5.3 防诈骗风险评估系统
**多维度风险评分**：
```
风险评分模型：
├── 历史理赔频率 (25%)
│   ├── 年度理赔次数
│   ├── 理赔时间间隔
│   └── 理赔金额模式
├── 报案行为特征 (30%)
│   ├── 报案时间规律
│   ├── 照片质量和角度
│   └── 描述详细程度
├── 客户基本信息 (25%)
│   ├── 投保时间长短
│   ├── 车辆使用年限
│   └── 个人信用记录
└── 外部关联信息 (20%)
    ├── 同业理赔记录
    ├── 社交网络分析
    └── 地理位置模式
```

**诈骗模式识别**：
- 虚假损坏检测：人为制造损坏的特征识别
- 重复理赔检测：跨公司、跨时间的重复索赔
- 照片造假检测：PS编辑、角度欺骗等识别
- 金额夸大检测：维修费用合理性分析

**动态风险控制**：
```
风险控制策略：
├── 绿色通道 (风险评分 > 85分)
│   ├── 自动审批
│   ├── 24小时内支付
│   └── 最少人工干预
├── 标准流程 (风险评分 60-85分)
│   ├── AI初审 + 人工复核
│   ├── 48小时内处理
│   └── 标准验证流程
├── 重点关注 (风险评分 40-59分)
│   ├── 人工详细审查
│   ├── 额外证据要求
│   └── 72小时内处理
└── 暂停处理 (风险评分 < 40分)
    ├── 深度调查
    ├── 现场查勘
    └── 专家评估
```

#### 3.5.4 特殊案件防范机制
**玻璃险快速理赔防范**：
- 损失金额阈值：< 5000港币进入快速通道
- 真实性检测：裂纹物理特征验证
- 重复检测：图像指纹识别防重复理赔
- 价格监控：维修费用合理性检查

**时间戳验证系统**：
- EXIF时间戳检查：拍摄时间与报案时间逻辑性
- 天气条件验证：照片光线与当时天气一致性
- 地理位置验证：GPS数据与报案地点一致性
- 设备信息分析：拍摄设备的合理性验证

---

## 四、多渠道导流策略

### 4.1 微信渠道
**微信小程序**：
- 简化页面，只显示报案入口
- 点击直接跳转统一报案网页
- 可选微信授权预填基本信息

**微信公众号**：
- 菜单设置"我要报案"直接跳转
- 关键词自动回复报案链接
- 客服对话提供报案链接

### 4.2 WhatsApp渠道
**自动化服务**：
- 客户发送消息自动回复报案链接
- AI客服简单引导后提供链接
- 人工客服协助并提供链接

### 4.3 邮件渠道
**邮件服务**：
- 自动回复邮件包含报案链接
- 标准邮件模板包含报案入口
- 案件通知邮件包含查看链接

### 4.4 电话渠道
**客服引导**：
- 电话客服引导客户访问报案网页
- 通话后发送报案链接短信
- 电话指导完成网页操作

---

## 五、防诈骗技术体系

### 5.1 多层次防护机制
**分层防护架构**：
```
┌─────────────────────────────────────┐
│           客户端防护层               │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │拍照规范 │ │上传限制 │ │实时验证 │ │
│  └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           AI分析防护层               │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │图像识别 │ │真实性检测│ │风险评分 │ │
│  └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           业务规则防护层             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │自动审批 │ │人工转介 │ │风险预警 │ │
│  └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           数据监控防护层             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │案件数据 │ │模式库   │ │监控日志 │ │
│  └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────┘
```

### 5.2 主要诈骗类型防范
**虚假损坏诈骗防范**：
- 人为制造损坏识别：检测工具痕迹、力度异常、位置可疑
- 既有损坏冒充检测：分析损坏边缘风化、污垢积累、修复痕迹
- 物理特征验证：裂纹形状、应力分布、扩展模式合理性

**照片证据造假防范**：
- PS编辑检测：像素级分析、压缩痕迹、编辑工具特征
- 角度欺骗识别：标准化拍摄要求、参照物比例验证
- 元数据验证：EXIF时间戳、设备信息、GPS位置一致性

**重复理赔诈骗防范**：
- 跨公司查重：HKFI数据共享、实时信息查询
- 图像指纹识别：多算法哈希、相似度比对
- 时间窗口监控：短期重复申请检测

**金额夸大诈骗防范**：
- 动态价格监控：标准价格库、市场价格对比
- 维修点信誉评估：历史合作记录、价格合理性统计
- 费用合理性分析：多方报价比较、异常费用预警

### 5.3 智能风险评估
**多维度评分体系**：
```
风险评分权重分配：
├── 历史理赔频率 (25%)
│   ├── 年度理赔次数
│   ├── 理赔时间间隔
│   └── 理赔金额模式
├── 报案行为特征 (30%)
│   ├── 报案时间规律
│   ├── 照片质量和角度
│   └── 描述详细程度
├── 客户基本信息 (25%)
│   ├── 投保时间长短
│   ├── 车辆使用年限
│   └── 个人信用记录
└── 外部关联信息 (20%)
    ├── 同业理赔记录
    ├── 社交网络分析
    └── 地理位置模式
```

**动态风险控制策略**：
- 绿色通道（风险评分>85分）：自动审批，24小时内支付
- 标准流程（60-85分）：AI初审+人工复核，48小时处理
- 重点关注（40-59分）：人工详细审查，额外证据要求
- 暂停处理（<40分）：深度调查，现场查勘，专家评估

### 5.4 实时监控预警
**系统性能监控**：
- AI模型准确率：实时监控识别准确率≥95%
- 处理速度：平均处理时间≤24小时
- 诈骗检出率：成功识别诈骗案件比例≥95%
- 误报率：错误标记正常案件比例≤5%

**异常模式预警**：
- 时间模式异常：集中报案、特定时间段异常
- 地理模式异常：特定区域高频理赔
- 网络关联异常：可疑客户关联网络
- 设备指纹异常：相同设备多次报案

---

## 六、服务商生态系统集成

### 6.1 基于3家合作商的服务管理系统
**系统定位与价值**：
```
精品服务商生态：
电子报案系统 ←→ 3家合作商管理系统 ←→ 网页端服务工具
    ↓                    ↓                    ↓
客户报案照片 ←→ 智能派单调度 ←→ 现场服务记录
    ↓                    ↓                    ↓
照片对照验证 ←→ 质量控制管理 ←→ 完整服务档案
```

**核心功能集成**：
- 精品服务商管理：基于现有3家合作商，建立高质量服务标准
- 网页端数字化：通过响应式网页实现服务过程全程数字化记录
- 照片对照验证：服务商现场照片与客户报案照片进行对照验证
- 流程标准化：建立标准化的服务流程和质量控制体系
- 数据完整性：确保从报案到完成的完整数据链条

### 6.2 现有3家合作商管理
**合作商基本情况**：
```
日本玻璃维修有限公司：
├── 服务区域：港岛区、九龙区
├── 专业特长：日系车辆玻璃维修
├── 服务能力：日处理20-30单
├── 响应时间：平均1.5小时
├── 完成时间：平均18小时
└── 客户满意度：92%

友邦玻璃服务有限公司：
├── 服务区域：新界区、大屿山
├── 专业特长：欧美车辆玻璃维修
├── 服务能力：日处理15-25单
├── 响应时间：平均2小时
├── 完成时间：平均20小时
└── 客户满意度：89%

信仪汽车玻璃有限公司：
├── 服务区域：全港覆盖
├── 专业特长：豪华车辆玻璃维修
├── 服务能力：日处理10-20单
├── 响应时间：平均1小时
├── 完成时间：平均16小时
└── 客户满意度：95%
```

**线下管理流程**：
- 合作商信息录入：手动录入基本信息到系统
- 网页端工具配置：为每家合作商配置网页端账号
- 技师培训：培训技师使用网页端工具
- 服务流程对接：建立派单通知和质量反馈机制

### 6.3 价格管理与防诈骗
**分类定价标准**：
```
车型分类定价：
├── 经济型车辆 (价值<30万港币)
│   ├── 前挡风玻璃：1500-3000港币
│   ├── 后挡风玻璃：1200-2500港币
│   └── 侧窗玻璃：800-1500港币
├── 中档车辆 (价值30-80万港币)
│   ├── 前挡风玻璃：2500-5000港币
│   ├── 后挡风玻璃：2000-4000港币
│   └── 侧窗玻璃：1200-2500港币
└── 豪华车辆 (价值>80万港币)
    ├── 前挡风玻璃：4000-10000港币
    ├── 后挡风玻璃：3000-8000港币
    └── 侧窗玻璃：2000-5000港币
```

**价格防诈骗控制**：
- 动态价格监控：实时监控价格异常和市场变化
- 多方报价对比：重要项目要求多家服务商报价
- 价格偏差控制：15%预警、25%告警、40%阻止
- 成本合理性分析：材料成本和人工成本合理性验证
- 历史价格分析：分析服务商历史报价趋势

### 6.4 智能派单与网页端服务工具
**基于3家合作商的派单策略**：
```
派单决策因素：
├── 服务区域匹配 (40%)
│   ├── 日本玻璃：港岛区、九龙区优先
│   ├── 友邦玻璃：新界区、大屿山优先
│   └── 信仪玻璃：全港覆盖，豪华车优先
├── 车辆专业匹配 (30%)
│   ├── 日系车辆 → 日本玻璃维修
│   ├── 欧美车辆 → 友邦玻璃服务
│   └── 豪华车辆 → 信仪汽车玻璃
├── 服务负载均衡 (20%)
└── 历史服务质量 (10%)
```

**网页端服务工具**：
- 响应式网页设计：适配手机/平板浏览器使用
- HTML5拍照功能：调用设备摄像头进行标准化拍摄
- 照片对照验证：与客户报案照片进行AI对比分析
- PWA离线功能：支持离线操作和数据同步
- 质量控制体系：实时质量检查和标准流程引导

### 6.5 系统集成接口
**与电子报案系统集成**：
```
API接口设计：
├── 案件信息接收：POST /api/v1/claims/receive
├── 服务进度更新：PUT /api/v1/claims/{claimId}/progress
├── 服务完成确认：POST /api/v1/claims/{claimId}/complete
└── 质量反馈同步：POST /api/v1/claims/{claimId}/feedback
```

**数据同步机制**：
- 实时数据同步：关键业务数据实时同步
- 状态变更通知：服务状态变更自动通知
- 质量评价反馈：客户评价数据及时反馈
- 异常情况预警：服务异常情况实时预警

---

## 七、数据管理与安全

### 5.1 数据存储架构
**数据类型**：
- 结构化数据：报案信息、客户信息、保单信息
- 非结构化数据：图片、视频、音频、文档文件
- 操作记录：完整的操作历史和审计日志

**存储策略**：
- 热数据：近3个月数据，高性能存储
- 温数据：3个月-2年数据，标准存储
- 冷数据：2年以上数据，归档存储

### 5.2 安全保障
**数据安全**：
- 传输加密：HTTPS/TLS 1.3加密传输
- 存储加密：敏感数据AES-256加密存储
- 访问控制：基于角色的权限管理
- 审计日志：完整的操作审计记录

**合规要求**：
- 香港个人资料（私隐）条例合规
- 保险业监管局(IA)要求
- 数据本地化：客户数据存储在香港境内
- 电子交易条例：符合电子认证标准

---

## 六、性能与可靠性要求

### 6.1 性能指标
**响应时间**：
- 页面加载时间≤3秒
- API接口响应≤500ms
- 文件上传响应≤1秒
- AI分析处理≤10秒

**并发能力**：
- 同时在线用户≥1000人
- 峰值并发处理≥2000人
- 日处理报案量≥10,000件

### 6.2 可靠性要求
**系统可用性**：
- 系统可用性≥99.9%
- 故障恢复时间≤30分钟
- 数据丢失率：0%
- 7×24小时连续运行

**容错能力**：
- 无单点故障设计
- 自动故障转移
- 数据实时备份
- 灾难恢复机制

---

## 七、项目实施计划

### 7.1 实施阶段
**第一阶段（1-3个月）**：核心报案网页开发
- 统一报案入口开发
- 身份认证系统
- 基础报案功能

**第二阶段（4-6个月）**：多渠道导流集成
- 微信、WhatsApp集成
- 邮件、电话渠道对接
- 客服系统集成

**第三阶段（7-9个月）**：智能化功能开发
- OCR识别功能
- 图像分析功能
- 风险评估系统

**第四阶段（10-12个月）**：系统优化完善
- 性能优化
- 安全加固
- 用户体验优化



---

## 八、验收标准

### 8.1 功能验收
- 统一报案网页功能完整
- 多渠道导流正常运行
- 智能处理功能达标
- 数据管理安全可靠

### 8.2 性能验收
- 响应时间达标率≥95%
- 系统可用性≥99.9%
- 并发处理能力达标
- 数据处理准确率≥95%

### 8.3 用户体验
- 用户满意度≥90%
- 操作流程简洁明了
- 错误提示清晰友好
- 多语言支持完善

---

**文档版本**：v1.0
**编制日期**：2025年1月
**编制部门**：财险理赔管理部
**审核状态**：待审核
**适用范围**：电子报案系统建设项目
