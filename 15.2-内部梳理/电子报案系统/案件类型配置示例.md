# 案件类型配置示例
## 电子报案系统案件类型配置参考

### 配置说明
本文档提供各种案件类型的具体配置示例，包括字段定义、验证规则、资料要求等，供系统开发和配置参考。

---

## 一、交通意外案件配置

### 1.1 字段配置
```json
{
  "caseType": "traffic_accident",
  "displayName": "交通意外",
  "icon": "car-crash",
  "description": "车辆发生碰撞、刮擦等交通事故",
  "fields": [
    {
      "fieldId": "accident_time",
      "fieldName": "事故发生时间",
      "fieldType": "datetime",
      "required": true,
      "validation": {
        "maxDate": "now",
        "minDate": "policy_start_date"
      },
      "placeholder": "请选择事故发生的具体时间"
    },
    {
      "fieldId": "accident_location",
      "fieldName": "事故发生地点",
      "fieldType": "location",
      "required": true,
      "enableGPS": true,
      "placeholder": "请输入详细地址或使用GPS定位"
    },
    {
      "fieldId": "weather_condition",
      "fieldName": "天气状况",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "sunny", "label": "晴天"},
        {"value": "rainy", "label": "雨天"},
        {"value": "foggy", "label": "雾天"},
        {"value": "snowy", "label": "雪天"},
        {"value": "other", "label": "其他"}
      ]
    },
    {
      "fieldId": "road_condition",
      "fieldName": "道路状况",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "dry", "label": "干燥"},
        {"value": "wet", "label": "湿滑"},
        {"value": "icy", "label": "结冰"},
        {"value": "construction", "label": "施工中"},
        {"value": "other", "label": "其他"}
      ]
    },
    {
      "fieldId": "driver_name",
      "fieldName": "驾驶员姓名",
      "fieldType": "text",
      "required": true,
      "validation": {
        "minLength": 2,
        "maxLength": 20
      }
    },
    {
      "fieldId": "driver_license",
      "fieldName": "驾驶证号码",
      "fieldType": "text",
      "required": true,
      "validation": {
        "pattern": "^[A-Z0-9]{8,15}$"
      }
    },
    {
      "fieldId": "vehicle_damage",
      "fieldName": "车辆损坏部位",
      "fieldType": "checkbox",
      "required": true,
      "options": [
        {"value": "front", "label": "车头"},
        {"value": "rear", "label": "车尾"},
        {"value": "left_side", "label": "左侧"},
        {"value": "right_side", "label": "右侧"},
        {"value": "roof", "label": "车顶"},
        {"value": "windshield", "label": "挡风玻璃"},
        {"value": "other", "label": "其他"}
      ]
    },
    {
      "fieldId": "third_party_involved",
      "fieldName": "是否涉及第三方",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ],
      "conditionalFields": {
        "yes": ["third_party_info", "third_party_vehicle", "third_party_insurance"]
      }
    },
    {
      "fieldId": "injury_involved",
      "fieldName": "是否有人员伤亡",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ],
      "conditionalFields": {
        "yes": ["injury_details", "hospital_info"]
      }
    },
    {
      "fieldId": "police_report",
      "fieldName": "是否报警",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ]
    }
  ]
}
```

### 1.2 资料要求配置
```json
{
  "requiredDocuments": [
    {
      "documentType": "scene_photos",
      "displayName": "现场照片",
      "required": true,
      "minCount": 2,
      "maxCount": 10,
      "description": "请上传事故现场全景照片，至少2张",
      "acceptedFormats": ["jpg", "jpeg", "png"],
      "maxFileSize": "10MB"
    },
    {
      "documentType": "vehicle_damage_photos",
      "displayName": "车辆损坏照片",
      "required": true,
      "minCount": 1,
      "maxCount": 8,
      "description": "请上传车辆损坏部位的清晰照片",
      "acceptedFormats": ["jpg", "jpeg", "png"],
      "maxFileSize": "10MB"
    },
    {
      "documentType": "driving_license",
      "displayName": "驾驶证",
      "required": true,
      "minCount": 1,
      "maxCount": 2,
      "description": "请上传驾驶证正反面照片",
      "acceptedFormats": ["jpg", "jpeg", "png"],
      "maxFileSize": "5MB",
      "ocrEnabled": true
    },
    {
      "documentType": "vehicle_registration",
      "displayName": "行驶证",
      "required": true,
      "minCount": 1,
      "maxCount": 2,
      "description": "请上传行驶证正反面照片",
      "acceptedFormats": ["jpg", "jpeg", "png"],
      "maxFileSize": "5MB",
      "ocrEnabled": true
    },
    {
      "documentType": "id_card",
      "displayName": "身份证",
      "required": true,
      "minCount": 1,
      "maxCount": 2,
      "description": "请上传身份证正反面照片",
      "acceptedFormats": ["jpg", "jpeg", "png"],
      "maxFileSize": "5MB",
      "ocrEnabled": true
    }
  ],
  "conditionalDocuments": [
    {
      "condition": "third_party_involved == 'yes'",
      "documents": [
        {
          "documentType": "third_party_photos",
          "displayName": "对方车辆照片",
          "required": true,
          "description": "请上传对方车辆及损坏情况照片"
        }
      ]
    },
    {
      "condition": "police_report == 'yes'",
      "documents": [
        {
          "documentType": "police_report_doc",
          "displayName": "交警事故认定书",
          "required": false,
          "description": "如已出具，请上传交警事故认定书"
        }
      ]
    }
  ]
}
```

---

## 二、玻璃损坏案件配置

### 2.1 字段配置
```json
{
  "caseType": "glass_damage",
  "displayName": "玻璃损坏",
  "icon": "glass-broken",
  "description": "车辆玻璃破损、裂纹等损坏",
  "quickClaimThreshold": 5000,
  "fields": [
    {
      "fieldId": "damage_time",
      "fieldName": "发现损坏时间",
      "fieldType": "datetime",
      "required": true,
      "placeholder": "请选择发现玻璃损坏的时间"
    },
    {
      "fieldId": "damage_location",
      "fieldName": "损坏发生地点",
      "fieldType": "location",
      "required": true,
      "enableGPS": true
    },
    {
      "fieldId": "damage_cause",
      "fieldName": "损坏原因",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "stone_hit", "label": "石子击打"},
        {"value": "temperature", "label": "高温爆裂"},
        {"value": "vandalism", "label": "人为损坏"},
        {"value": "unknown", "label": "不明原因"},
        {"value": "other", "label": "其他"}
      ]
    },
    {
      "fieldId": "glass_position",
      "fieldName": "损坏玻璃位置",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "front_windshield", "label": "前挡风玻璃"},
        {"value": "rear_windshield", "label": "后挡风玻璃"},
        {"value": "left_front", "label": "左前窗"},
        {"value": "left_rear", "label": "左后窗"},
        {"value": "right_front", "label": "右前窗"},
        {"value": "right_rear", "label": "右后窗"},
        {"value": "sunroof", "label": "天窗"}
      ]
    },
    {
      "fieldId": "damage_extent",
      "fieldName": "损坏程度",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "crack", "label": "裂纹"},
        {"value": "hole", "label": "破洞"},
        {"value": "shattered", "label": "完全破碎"}
      ]
    },
    {
      "fieldId": "driving_safety",
      "fieldName": "是否影响驾驶安全",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ]
    },
    {
      "fieldId": "glass_brand",
      "fieldName": "玻璃品牌",
      "fieldType": "text",
      "required": false,
      "placeholder": "如知道请填写，不知道可留空"
    },
    {
      "fieldId": "has_film",
      "fieldName": "是否有贴膜",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ]
    }
  ]
}
```

### 2.2 快速理赔与防诈骗规则
```json
{
  "quickClaimRules": {
    "enabled": true,
    "conditions": [
      {
        "field": "estimated_amount",
        "operator": "<=",
        "value": 5000,
        "currency": "HKD"
      },
      {
        "field": "damage_cause",
        "operator": "in",
        "value": ["stone_hit", "temperature", "unknown"]
      },
      {
        "field": "glass_position",
        "operator": "!=",
        "value": "sunroof"
      },
      {
        "field": "risk_score",
        "operator": ">=",
        "value": 85
      }
    ],
    "autoApproval": true,
    "processingTime": "24小时",
    "requiredDocuments": ["glass_damage_photos", "vehicle_registration", "id_card"]
  },
  "fraudDetectionRules": {
    "imageQualityChecks": {
      "minResolution": [1920, 1080],
      "maxBlurThreshold": 100,
      "requiredAngles": ["front_view", "side_view"],
      "referenceObjectRequired": true
    },
    "damageAuthenticityChecks": {
      "crackPatternAnalysis": true,
      "physicsValidation": true,
      "edgeCharacteristics": true,
      "stressDistribution": true
    },
    "duplicateDetection": {
      "imageFingerprinting": true,
      "crossCompanyCheck": true,
      "timeWindowDays": 365,
      "similarityThreshold": 0.85
    },
    "riskFactors": [
      {
        "factor": "claim_frequency",
        "weight": 0.25,
        "thresholds": {
          "low": 0,
          "medium": 2,
          "high": 4
        }
      },
      {
        "factor": "photo_quality",
        "weight": 0.20,
        "checks": ["resolution", "clarity", "angle", "lighting"]
      },
      {
        "factor": "damage_consistency",
        "weight": 0.30,
        "validations": ["physics", "pattern", "location"]
      },
      {
        "factor": "customer_history",
        "weight": 0.25,
        "factors": ["tenure", "claims_ratio", "payment_history"]
      }
    ]
  }
}
```

---

## 三、火灾损失案件配置

### 3.1 字段配置
```json
{
  "caseType": "fire_damage",
  "displayName": "火灾损失",
  "icon": "fire",
  "description": "因火灾造成的财产损失",
  "highRiskCase": true,
  "fields": [
    {
      "fieldId": "fire_time",
      "fieldName": "火灾发生时间",
      "fieldType": "datetime",
      "required": true
    },
    {
      "fieldId": "fire_location",
      "fieldName": "火灾发生地点",
      "fieldType": "address",
      "required": true,
      "placeholder": "请输入详细地址"
    },
    {
      "fieldId": "fire_cause",
      "fieldName": "火灾原因",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "electrical", "label": "电器故障"},
        {"value": "gas_leak", "label": "燃气泄漏"},
        {"value": "external", "label": "外来火源"},
        {"value": "lightning", "label": "雷击"},
        {"value": "unknown", "label": "不明原因"},
        {"value": "other", "label": "其他"}
      ]
    },
    {
      "fieldId": "fire_department_called",
      "fieldName": "是否报警/消防部门是否到场",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ]
    },
    {
      "fieldId": "casualties",
      "fieldName": "是否有人员伤亡",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ]
    },
    {
      "fieldId": "property_list",
      "fieldName": "受损财产清单",
      "fieldType": "textarea",
      "required": true,
      "placeholder": "请详细列出受损的财产及其价值",
      "minLength": 50
    },
    {
      "fieldId": "estimated_loss",
      "fieldName": "预估总损失金额",
      "fieldType": "currency",
      "required": true,
      "currency": "HKD",
      "validation": {
        "min": 0,
        "max": 10000000
      }
    }
  ]
}
```

### 3.2 特殊处理规则
```json
{
  "specialProcessing": {
    "highValueThreshold": 100000,
    "expertAssessmentRequired": true,
    "mandatoryDocuments": [
      "fire_department_certificate",
      "police_report",
      "property_receipts"
    ],
    "processingTime": "7-14个工作日",
    "approvalLevel": "senior_manager"
  }
}
```

---

## 四、意外伤害案件配置

### 4.1 字段配置
```json
{
  "caseType": "personal_injury",
  "displayName": "意外伤害",
  "icon": "user-injured",
  "description": "因意外事故造成的人身伤害",
  "sensitiveCase": true,
  "fields": [
    {
      "fieldId": "accident_time",
      "fieldName": "意外发生时间",
      "fieldType": "datetime",
      "required": true
    },
    {
      "fieldId": "accident_location",
      "fieldName": "意外发生地点",
      "fieldType": "address",
      "required": true
    },
    {
      "fieldId": "accident_cause",
      "fieldName": "意外原因",
      "fieldType": "textarea",
      "required": true,
      "placeholder": "请详细描述意外发生的经过",
      "minLength": 30
    },
    {
      "fieldId": "injury_parts",
      "fieldName": "受伤部位",
      "fieldType": "checkbox",
      "required": true,
      "options": [
        {"value": "head", "label": "头部"},
        {"value": "neck", "label": "颈部"},
        {"value": "chest", "label": "胸部"},
        {"value": "back", "label": "背部"},
        {"value": "arms", "label": "手臂"},
        {"value": "legs", "label": "腿部"},
        {"value": "other", "label": "其他"}
      ]
    },
    {
      "fieldId": "injury_severity",
      "fieldName": "伤势程度",
      "fieldType": "select",
      "required": true,
      "options": [
        {"value": "minor", "label": "轻微"},
        {"value": "moderate", "label": "中等"},
        {"value": "severe", "label": "严重"}
      ]
    },
    {
      "fieldId": "immediate_treatment",
      "fieldName": "是否立即就医",
      "fieldType": "radio",
      "required": true,
      "options": [
        {"value": "yes", "label": "是"},
        {"value": "no", "label": "否"}
      ]
    },
    {
      "fieldId": "hospital_name",
      "fieldName": "就医医院名称",
      "fieldType": "text",
      "required": true,
      "dependsOn": "immediate_treatment",
      "dependsValue": "yes"
    },
    {
      "fieldId": "diagnosis",
      "fieldName": "诊断结果",
      "fieldType": "textarea",
      "required": false,
      "placeholder": "请填写医生的诊断结果"
    }
  ]
}
```

---

## 五、配置管理系统

### 5.1 配置版本控制
```json
{
  "configVersion": "1.0.0",
  "lastUpdated": "2025-01-11",
  "updatedBy": "system_admin",
  "changeLog": [
    {
      "version": "1.0.0",
      "date": "2025-01-11",
      "changes": "初始配置创建",
      "author": "system_admin"
    }
  ]
}
```

### 5.2 A/B测试配置
```json
{
  "abTestEnabled": true,
  "testGroups": [
    {
      "groupName": "control",
      "percentage": 50,
      "config": "standard_form"
    },
    {
      "groupName": "experimental",
      "percentage": 50,
      "config": "simplified_form"
    }
  ]
}
```

---

**总结**：通过详细的配置示例，开发团队可以准确理解每种案件类型的具体要求，确保系统能够正确处理各种不同的报案情况。
